package com.example.habits9.ui.details.components

import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for ScoreChart component.
 */
class ScoreChartTest {

    @Test
    fun scorePoint_creation_isCorrect() {
        val timestamp = System.currentTimeMillis()
        val score = 0.75
        
        val scorePoint = ScorePoint(timestamp, score)
        
        assertEquals(timestamp, scorePoint.timestamp)
        assertEquals(score, scorePoint.value, 0.001)
    }

    @Test
    fun scorePoint_valueRange_isValid() {
        val timestamp = System.currentTimeMillis()
        
        // Test valid range (0.0 to 1.0)
        val validScores = listOf(0.0, 0.25, 0.5, 0.75, 1.0)
        
        validScores.forEach { score ->
            val scorePoint = ScorePoint(timestamp, score)
            assertTrue("Score $score should be valid", scorePoint.value >= 0.0 && scorePoint.value <= 1.0)
        }
    }

    @Test
    fun scorePoint_edgeCases_handleCorrectly() {
        val timestamp = System.currentTimeMillis()

        // Test edge cases
        val minScore = ScorePoint(timestamp, 0.0)
        val maxScore = ScorePoint(timestamp, 1.0)

        assertEquals(0.0, minScore.value, 0.001)
        assertEquals(1.0, maxScore.value, 0.001)
    }

    @Test
    fun scorePoint_percentageConversion_isCorrect() {
        val timestamp = System.currentTimeMillis()

        // Test percentage conversion
        val scores = listOf(
            ScorePoint(timestamp, 0.0),   // 0%
            ScorePoint(timestamp, 0.25),  // 25%
            ScorePoint(timestamp, 0.5),   // 50%
            ScorePoint(timestamp, 0.75),  // 75%
            ScorePoint(timestamp, 1.0)    // 100%
        )

        val expectedPercentages = listOf(0, 25, 50, 75, 100)

        scores.forEachIndexed { index, score ->
            val percentage = (score.value * 100).toInt()
            assertEquals("Score ${score.value} should convert to ${expectedPercentages[index]}%",
                expectedPercentages[index], percentage)
        }
    }

    @Test
    fun scoreChart_scrollableData_handlesLargeDatasets() {
        val baseTimestamp = System.currentTimeMillis()

        // Create a large dataset to test scrolling
        val largeScoreList = (0..20).map { index ->
            ScorePoint(
                timestamp = baseTimestamp + (index * 86400000L), // Daily intervals
                value = (index % 5) * 0.25 // Varying scores
            )
        }

        assertTrue("Should handle large datasets", largeScoreList.size > 10)

        // Verify all scores are valid
        largeScoreList.forEach { score ->
            assertTrue("Score should be between 0.0 and 1.0",
                score.value >= 0.0 && score.value <= 1.0)
        }
    }

    @Test
    fun scoreChart_timePeriodLabeling_isCorrect() {
        // Test that different time periods can be handled
        val timePeriods = listOf(
            com.example.habits9.data.analytics.TimePeriod.WEEK,
            com.example.habits9.data.analytics.TimePeriod.MONTH,
            com.example.habits9.data.analytics.TimePeriod.QUARTER,
            com.example.habits9.data.analytics.TimePeriod.YEAR
        )

        timePeriods.forEach { period ->
            assertNotNull("TimePeriod $period should be valid", period)
        }
    }
}
